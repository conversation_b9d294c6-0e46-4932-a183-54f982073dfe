#!/usr/bin/env tsx

/**
 * Appwrite Configuration Diagnostic Script
 * 
 * Run this script to diagnose issues with your Appwrite setup:
 * npx tsx src/scripts/diagnose-appwrite.ts
 */

import { SessionManager } from '../lib/auth-utils';
import { generateAppwriteCLICommands, SETUP_INSTRUCTIONS } from '../lib/appwrite-schema';

async function diagnoseAppwrite() {
  console.log('🔍 Diagnosing Appwrite Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  const requiredEnvVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'NEXT_PUBLIC_APPWRITE_DATABASE_ID',
    'NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_VMS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_SESSIONS_COLLECTION_ID',
    'NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID',
    'APPWRITE_API_KEY'
  ];

  let envIssues = 0;
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: ${envVar.includes('API_KEY') ? '***' : value}`);
    } else {
      console.log(`❌ ${envVar}: Missing`);
      envIssues++;
    }
  }

  if (envIssues > 0) {
    console.log(`\n⚠️  Found ${envIssues} missing environment variables.`);
    console.log('Please add them to your .env.local file.\n');
  } else {
    console.log('\n✅ All environment variables are configured.\n');
  }

  // Check database schema
  console.log('🗄️  Database Schema:');
  try {
    const diagnosis = await SessionManager.diagnoseDatabaseSchema();
    
    if (diagnosis.isConfigured) {
      console.log('✅ Database schema is properly configured.');
    } else {
      console.log('❌ Database schema issues found:');
      diagnosis.issues.forEach(issue => console.log(`   - ${issue}`));
      
      console.log('\n💡 Recommendations:');
      diagnosis.recommendations.forEach(rec => console.log(`   - ${rec}`));
    }
  } catch (error) {
    console.log(`❌ Failed to check database schema: ${error}`);
  }

  // Generate CLI commands
  console.log('\n🛠️  Setup Commands:');
  const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || 'YOUR_PROJECT_ID';
  const databaseId = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'YOUR_DATABASE_ID';
  
  console.log('If you need to create the collections, run these Appwrite CLI commands:\n');
  const commands = generateAppwriteCLICommands(projectId, databaseId);
  commands.forEach(command => console.log(command + '\n'));

  // Show setup instructions
  console.log('📖 Setup Instructions:');
  console.log(SETUP_INSTRUCTIONS);
}

// Run the diagnostic
diagnoseAppwrite().catch(error => {
  console.error('❌ Diagnostic failed:', error);
  process.exit(1);
});
