# Database Schema Mapping: Appwrite to Prisma

## Overview

This document maps the current Appwrite collections to equivalent Prisma schemas for PostgreSQL/MySQL. The mapping considers Better Auth's core schema requirements and extends them with application-specific fields.

## Better Auth Core Schema

Better Auth requires these core tables:

### Core Tables (Required by Better Auth)

```prisma
// User table (Better Auth core)
model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  accounts      Account[]
  sessions      Session[]
  
  @@map("user")
}

// Session table (Better Auth core)
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("session")
}

// Account table (Better Auth core)
model Account {
  id                    String    @id @default(cuid())
  userId                String
  accountId             String
  providerId            String
  accessToken           String?
  refreshToken          String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  idToken               String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("account")
}

// Verification table (Better Auth core)
model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@map("verification")
}
```

## Extended Schema for Omnispace

### User Profile Extension

**Current Appwrite Users Collection → Extended User Model**

```prisma
model User {
  // Better Auth core fields
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Extended profile fields (from Appwrite users collection)
  phone         String?
  bio           String?
  company       String?
  location      String?
  website       String?
  
  // Subscription information
  subscriptionPlan     String    @default("free") // free, professional, enterprise
  subscriptionStatus   String    @default("active") // active, inactive, cancelled
  subscriptionStart    DateTime?
  subscriptionEnd      DateTime?
  
  // Usage tracking
  storageUsed          BigInt    @default(0)
  computeUsed          BigInt    @default(0)
  apiCallsUsed         Int       @default(0)
  
  // Preferences
  theme                String    @default("system") // light, dark, system
  language             String    @default("en")
  timezone             String    @default("UTC")
  notifications        Json      @default("{}")
  
  // Security settings
  twoFactorEnabled     Boolean   @default(false)
  twoFactorSecret      String?
  lastPasswordChange   DateTime?
  loginSessions        Int       @default(0)
  
  // Relations
  accounts             Account[]
  sessions             Session[]
  vms                  VirtualMachine[]
  workspaces           Workspace[]
  workspaceMembers     WorkspaceMember[]
  
  @@map("user")
}
```

### Virtual Machines

**Current Appwrite VMs Collection → VirtualMachine Model**

```prisma
model VirtualMachine {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  
  // VM Configuration
  type        String   // python, node, docker, etc.
  framework   String?  // django, fastapi, express, etc.
  version     String?
  
  // Resource allocation
  cpu         Int      @default(1)
  memory      Int      @default(512) // MB
  storage     Int      @default(1024) // MB
  
  // Status and metadata
  status      String   @default("stopped") // running, stopped, creating, error
  port        Int?
  url         String?
  
  // Configuration
  environment Json     @default("{}")
  packages    Json     @default("[]")
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastUsed    DateTime?
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  executions  VMExecution[]
  
  @@map("virtual_machine")
}

model VMExecution {
  id            String   @id @default(cuid())
  vmId          String
  command       String
  output        String?
  error         String?
  exitCode      Int?
  status        String   @default("running") // running, completed, failed
  executionTime Int?     // milliseconds
  createdAt     DateTime @default(now())
  
  // Relations
  vm            VirtualMachine @relation(fields: [vmId], references: [id], onDelete: Cascade)
  
  @@map("vm_execution")
}
```

### Workspace Management

**Current Appwrite Workspace Collections → Workspace Models**

```prisma
model Workspace {
  id          String   @id @default(cuid())
  ownerId     String
  name        String
  description String?
  
  // Workspace settings
  visibility  String   @default("private") // private, public, team
  settings    Json     @default("{}")
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  owner       User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  members     WorkspaceMember[]
  files       WorkspaceFile[]
  sessions    WorkspaceSession[]
  
  @@map("workspace")
}

model WorkspaceMember {
  id          String   @id @default(cuid())
  workspaceId String
  userId      String
  role        String   @default("member") // owner, admin, member, viewer
  permissions Json     @default("{}")
  joinedAt    DateTime @default(now())
  
  // Relations
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([workspaceId, userId])
  @@map("workspace_member")
}

model WorkspaceFile {
  id          String   @id @default(cuid())
  workspaceId String
  name        String
  path        String
  content     String?
  size        Int      @default(0)
  mimeType    String?
  
  // File metadata
  isDirectory Boolean  @default(false)
  parentId    String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  parent      WorkspaceFile? @relation("FileHierarchy", fields: [parentId], references: [id])
  children    WorkspaceFile[] @relation("FileHierarchy")
  
  @@map("workspace_file")
}

model WorkspaceSession {
  id          String   @id @default(cuid())
  workspaceId String
  userId      String
  sessionData Json     @default("{}")
  isActive    Boolean  @default(true)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  expiresAt   DateTime
  
  // Relations
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  @@map("workspace_session")
}
```

## Migration Mapping Table

| Appwrite Collection | Prisma Model | Key Changes |
|-------------------|--------------|-------------|
| `users` | `User` | Extended with Better Auth core fields |
| `sessions` | `Session` | Replaced with Better Auth session model |
| `vms` | `VirtualMachine` | Restructured with better relations |
| `workspaces` | `Workspace` | Split into multiple related models |
| `workspace-permissions` | `WorkspaceMember` | Simplified permission model |
| `workspace-collaborators` | `WorkspaceMember` | Merged with permissions |
| `workspace-files` | `WorkspaceFile` | Added hierarchical structure |
| `workspace-sessions` | `WorkspaceSession` | Simplified session tracking |
| `workspace-executions` | `VMExecution` | Moved to VM-specific model |

## Data Transformation Requirements

### User Data Migration

```typescript
// Appwrite User → Prisma User transformation
const transformUser = (appwriteUser: any) => ({
  id: appwriteUser.$id,
  name: appwriteUser.name,
  email: appwriteUser.email,
  emailVerified: appwriteUser.emailVerification || false,
  image: appwriteUser.avatar,
  phone: appwriteUser.phone,
  bio: appwriteUser.bio,
  company: appwriteUser.company,
  location: appwriteUser.location,
  website: appwriteUser.website,
  subscriptionPlan: appwriteUser.subscription?.plan || 'free',
  subscriptionStatus: appwriteUser.subscription?.status || 'active',
  theme: appwriteUser.preferences?.theme || 'system',
  language: appwriteUser.preferences?.language || 'en',
  timezone: appwriteUser.preferences?.timezone || 'UTC',
  twoFactorEnabled: appwriteUser.security?.twoFactorEnabled || false,
  createdAt: new Date(appwriteUser.$createdAt),
  updatedAt: new Date(appwriteUser.$updatedAt),
});
```

### Session Data Migration

```typescript
// Appwrite Session → Better Auth Session transformation
const transformSession = (appwriteSession: any) => ({
  id: generateId(),
  userId: appwriteSession.userId,
  token: generateSecureToken(),
  expiresAt: new Date(appwriteSession.expire),
  ipAddress: appwriteSession.ip,
  userAgent: appwriteSession.userAgent,
  createdAt: new Date(appwriteSession.$createdAt),
  updatedAt: new Date(appwriteSession.$updatedAt),
});
```

## Next Steps

1. **Create Prisma schema file**
2. **Set up database migrations**
3. **Implement data transformation scripts**
4. **Create data validation procedures**
5. **Plan incremental migration strategy**
