# Comprehensive Migration Strategy: Appwrite to Better Auth + Prisma

## Executive Summary

This document outlines a detailed, step-by-step migration strategy to transition from Appwrite authentication and database to Better Auth with Prisma ORM and PostgreSQL/MySQL. The strategy emphasizes minimal downtime, data integrity, and rollback capabilities.

## Migration Approach: Parallel System Strategy

### Core Principles

1. **Zero Downtime**: Run both systems in parallel during transition
2. **Data Integrity**: Ensure no data loss during migration
3. **Rollback Ready**: Maintain ability to revert at any stage
4. **Incremental Migration**: Migrate features progressively
5. **Comprehensive Testing**: Validate each step thoroughly

## Phase 1: Foundation Setup (Week 1-2)

### Week 1: Environment Preparation

#### Day 1-2: Database Setup
```bash
# 1. Set up PostgreSQL/MySQL database
# For PostgreSQL (recommended)
createdb omnispace_new

# 2. Initialize Prisma
npm install prisma @prisma/client
npx prisma init

# 3. Configure database connection
# DATABASE_URL="postgresql://user:password@localhost:5432/omnispace_new"
```

#### Day 3-4: Better Auth Installation
```bash
# 1. Install Better Auth
npm install better-auth

# 2. Install required adapters and plugins
npm install better-auth/adapters/prisma
npm install better-auth/plugins/two-factor
npm install better-auth/plugins/magic-link
```

#### Day 5-7: Initial Configuration
```typescript
// lib/auth.ts - Better Auth configuration
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { twoFactor } from "better-auth/plugins";
import { prisma } from "./prisma";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql", // or "mysql"
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
  },
  plugins: [
    twoFactor({
      issuer: "Omnispace",
    }),
  ],
  user: {
    additionalFields: {
      phone: { type: "string", required: false },
      bio: { type: "string", required: false },
      company: { type: "string", required: false },
      location: { type: "string", required: false },
      website: { type: "string", required: false },
      subscriptionPlan: { type: "string", required: false, defaultValue: "free" },
      subscriptionStatus: { type: "string", required: false, defaultValue: "active" },
      theme: { type: "string", required: false, defaultValue: "system" },
      language: { type: "string", required: false, defaultValue: "en" },
      timezone: { type: "string", required: false, defaultValue: "UTC" },
    },
  },
});
```

### Week 2: Schema Development

#### Day 8-10: Prisma Schema Creation
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // or "mysql"
  url      = env("DATABASE_URL")
}

// Better Auth core tables + extended fields
// (See DATABASE_SCHEMA_MAPPING.md for complete schema)
```

#### Day 11-12: Database Migration Setup
```bash
# Generate and run initial migration
npx prisma migrate dev --name init
npx prisma generate
```

#### Day 13-14: Compatibility Layer Development
```typescript
// lib/auth-adapter.ts - Compatibility layer
export interface AuthAdapter {
  login(email: string, password: string): Promise<AuthResult>;
  register(userData: RegisterData): Promise<AuthResult>;
  logout(): Promise<void>;
  getCurrentUser(): Promise<User | null>;
}

export class AppwriteAuthAdapter implements AuthAdapter {
  // Current Appwrite implementation
}

export class BetterAuthAdapter implements AuthAdapter {
  // New Better Auth implementation
}

// Feature flag controlled adapter
export const getAuthAdapter = (): AuthAdapter => {
  return process.env.USE_BETTER_AUTH === 'true' 
    ? new BetterAuthAdapter()
    : new AppwriteAuthAdapter();
};
```

## Phase 2: Core Authentication Migration (Week 3-4)

### Week 3: Authentication System Setup

#### Day 15-17: Better Auth API Routes
```typescript
// app/api/auth/[...all]/route.ts
import { auth } from "@/lib/auth";
import { toNextJsHandler } from "better-auth/next-js";

export const { POST, GET } = toNextJsHandler(auth);
```

#### Day 18-19: Client-Side Integration
```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL,
});

// Compatibility wrapper
export const useAuth = () => {
  const { data: session, isPending } = authClient.useSession();
  
  return {
    user: session?.user || null,
    isLoading: isPending,
    isAuthenticated: !!session?.user,
    login: async (email: string, password: string) => {
      return authClient.signIn.email({ email, password });
    },
    register: async (userData: any) => {
      return authClient.signUp.email(userData);
    },
    logout: async () => {
      return authClient.signOut();
    },
  };
};
```

#### Day 20-21: Email Service Integration
```typescript
// lib/email-service.ts
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export const emailService = {
  sendVerificationEmail: async ({ user, url, token }) => {
    await resend.emails.send({
      from: '<EMAIL>',
      to: user.email,
      subject: 'Verify your email',
      html: `<a href="${url}">Verify Email</a>`,
    });
  },
  
  sendPasswordReset: async ({ user, url, token }) => {
    await resend.emails.send({
      from: '<EMAIL>',
      to: user.email,
      subject: 'Reset your password',
      html: `<a href="${url}">Reset Password</a>`,
    });
  },
};
```

### Week 4: Testing & Validation

#### Day 22-24: Unit Testing
```typescript
// __tests__/auth.test.ts
import { describe, it, expect } from 'vitest';
import { auth } from '@/lib/auth';

describe('Better Auth Integration', () => {
  it('should create user account', async () => {
    const result = await auth.api.signUpEmail({
      body: {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      },
    });
    
    expect(result.user).toBeDefined();
    expect(result.user.email).toBe('<EMAIL>');
  });
  
  it('should authenticate user', async () => {
    const result = await auth.api.signInEmail({
      body: {
        email: '<EMAIL>',
        password: 'password123',
      },
    });
    
    expect(result.session).toBeDefined();
    expect(result.user).toBeDefined();
  });
});
```

#### Day 25-28: Integration Testing
- Test OAuth flows
- Test email verification
- Test password reset
- Test session management
- Test 2FA functionality

## Phase 3: Data Migration (Week 5-6)

### Week 5: Data Export & Transformation

#### Day 29-31: Appwrite Data Export
```typescript
// scripts/export-appwrite-data.ts
import { Client, Databases, Users } from 'node-appwrite';

const client = new Client()
  .setEndpoint(process.env.APPWRITE_ENDPOINT!)
  .setProject(process.env.APPWRITE_PROJECT_ID!)
  .setKey(process.env.APPWRITE_API_KEY!);

const databases = new Databases(client);
const users = new Users(client);

export async function exportUsers() {
  const allUsers = [];
  let offset = 0;
  const limit = 100;
  
  while (true) {
    const response = await users.list([], limit, offset);
    allUsers.push(...response.users);
    
    if (response.users.length < limit) break;
    offset += limit;
  }
  
  return allUsers;
}

export async function exportCollection(collectionId: string) {
  const allDocuments = [];
  let offset = 0;
  const limit = 100;
  
  while (true) {
    const response = await databases.listDocuments(
      process.env.APPWRITE_DATABASE_ID!,
      collectionId,
      [],
      limit,
      offset
    );
    
    allDocuments.push(...response.documents);
    
    if (response.documents.length < limit) break;
    offset += limit;
  }
  
  return allDocuments;
}
```

#### Day 32-33: Data Transformation Scripts
```typescript
// scripts/transform-data.ts
export function transformUser(appwriteUser: any) {
  return {
    id: appwriteUser.$id,
    name: appwriteUser.name,
    email: appwriteUser.email,
    emailVerified: appwriteUser.emailVerification || false,
    image: appwriteUser.avatar,
    phone: appwriteUser.phone,
    bio: appwriteUser.bio,
    company: appwriteUser.company,
    location: appwriteUser.location,
    website: appwriteUser.website,
    subscriptionPlan: appwriteUser.subscription?.plan || 'free',
    subscriptionStatus: appwriteUser.subscription?.status || 'active',
    theme: appwriteUser.preferences?.theme || 'system',
    language: appwriteUser.preferences?.language || 'en',
    timezone: appwriteUser.preferences?.timezone || 'UTC',
    createdAt: new Date(appwriteUser.$createdAt),
    updatedAt: new Date(appwriteUser.$updatedAt),
  };
}

export function transformVM(appwriteVM: any) {
  return {
    id: appwriteVM.$id,
    userId: appwriteVM.userId,
    name: appwriteVM.name,
    description: appwriteVM.description,
    type: appwriteVM.type,
    framework: appwriteVM.framework,
    version: appwriteVM.version,
    cpu: appwriteVM.cpu || 1,
    memory: appwriteVM.memory || 512,
    storage: appwriteVM.storage || 1024,
    status: appwriteVM.status || 'stopped',
    port: appwriteVM.port,
    url: appwriteVM.url,
    environment: appwriteVM.environment || {},
    packages: appwriteVM.packages || [],
    createdAt: new Date(appwriteVM.$createdAt),
    updatedAt: new Date(appwriteVM.$updatedAt),
    lastUsed: appwriteVM.lastUsed ? new Date(appwriteVM.lastUsed) : null,
  };
}
```

#### Day 34-35: Data Validation Scripts
```typescript
// scripts/validate-migration.ts
export async function validateUserMigration() {
  const appwriteUsers = await exportUsers();
  const prismaUsers = await prisma.user.findMany();
  
  console.log(`Appwrite users: ${appwriteUsers.length}`);
  console.log(`Prisma users: ${prismaUsers.length}`);
  
  // Validate data integrity
  for (const appwriteUser of appwriteUsers) {
    const prismaUser = prismaUsers.find(u => u.id === appwriteUser.$id);
    if (!prismaUser) {
      console.error(`Missing user: ${appwriteUser.$id}`);
    } else {
      // Validate field mappings
      if (prismaUser.email !== appwriteUser.email) {
        console.error(`Email mismatch for user ${appwriteUser.$id}`);
      }
    }
  }
}
```

### Week 6: Data Import & Validation

#### Day 36-38: Data Import Process
```typescript
// scripts/import-data.ts
export async function importUsers() {
  const appwriteUsers = await exportUsers();
  
  for (const appwriteUser of appwriteUsers) {
    const transformedUser = transformUser(appwriteUser);
    
    try {
      await prisma.user.create({
        data: transformedUser,
      });
      console.log(`Imported user: ${transformedUser.email}`);
    } catch (error) {
      console.error(`Failed to import user ${transformedUser.email}:`, error);
    }
  }
}

export async function importVMs() {
  const appwriteVMs = await exportCollection('vms');
  
  for (const appwriteVM of appwriteVMs) {
    const transformedVM = transformVM(appwriteVM);
    
    try {
      await prisma.virtualMachine.create({
        data: transformedVM,
      });
      console.log(`Imported VM: ${transformedVM.name}`);
    } catch (error) {
      console.error(`Failed to import VM ${transformedVM.name}:`, error);
    }
  }
}
```

#### Day 39-42: Comprehensive Validation
- Data integrity checks
- Performance testing
- Security validation
- User acceptance testing

## Phase 4: Production Deployment (Week 7-8)

### Week 7: Pre-deployment Preparation

#### Day 43-45: Production Environment Setup
- Set up production database
- Configure environment variables
- Set up monitoring and logging
- Prepare rollback procedures

#### Day 46-49: Final Testing
- Load testing
- Security audit
- End-to-end testing
- Rollback testing

### Week 8: Deployment & Monitoring

#### Day 50-52: Staged Deployment
1. Deploy to staging environment
2. Run final validation tests
3. Deploy to production with feature flags
4. Gradual rollout to user segments

#### Day 53-56: Post-deployment Monitoring
- Monitor system performance
- Track error rates
- Validate user experience
- Address any issues

## Risk Mitigation Strategies

### 1. Rollback Plan
```typescript
// Feature flag for instant rollback
const useNewAuth = process.env.USE_BETTER_AUTH === 'true' && 
                   !process.env.EMERGENCY_ROLLBACK;

export const authAdapter = useNewAuth 
  ? new BetterAuthAdapter() 
  : new AppwriteAuthAdapter();
```

### 2. Data Backup Strategy
- Full Appwrite data export before migration
- Database snapshots at each phase
- Incremental backups during migration

### 3. Monitoring & Alerting
```typescript
// Monitor key metrics
const metrics = {
  authSuccessRate: 0,
  authLatency: 0,
  errorRate: 0,
  userSatisfaction: 0,
};

// Alert thresholds
if (metrics.authSuccessRate < 0.95) {
  // Trigger rollback consideration
}
```

## Success Criteria

- [ ] 100% user data migrated successfully
- [ ] Authentication success rate > 99%
- [ ] System performance equal or better than current
- [ ] Zero data loss
- [ ] All features working as expected
- [ ] User experience maintained or improved
- [ ] Rollback capability verified and ready

## Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1 | Week 1-2 | Foundation setup, schema design |
| Phase 2 | Week 3-4 | Authentication system migration |
| Phase 3 | Week 5-6 | Data migration and validation |
| Phase 4 | Week 7-8 | Production deployment |

**Total Duration**: 8 weeks
**Risk Level**: Medium (with proper mitigation)
**Rollback Capability**: Available at all phases
